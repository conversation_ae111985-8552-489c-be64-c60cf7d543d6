"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useAppDispatch, useAppSelector } from "@/lib/hooks"
import { resetAppointment } from "@/store/features/appointmentSlice"

export function AppointmentConfirmation() {
  const dispatch = useAppDispatch()
  const appointmentData = useAppSelector((state) => state.appointment.appointmentData)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("de-DE", {
      day: "2-digit",
      month: "long",
      year: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    return `${timeString} Uhr`
  }

  const getServicesText = () => {
    return appointmentData.services.map((service) => service.name).join(", ")
  }

  const handleReset = () => {
    dispatch(resetAppointment())
  }

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Ihr Termin ist bestätigt.</CardTitle>
          <p className="text-gray-600 mt-2">Wir freuen uns auf Ihren Besuch</p>
        </CardHeader>
        <CardContent className="p-6 flex-1 flex flex-col">
          <div className="space-y-4 mb-6">
            <div>
              <span className="font-semibold">Datum:</span> {formatDate(appointmentData.date)}
            </div>
            <div>
              <span className="font-semibold">Uhrzeit:</span> {formatTime(appointmentData.time)}
            </div>
            <div>
              <span className="font-semibold">Gebuchte Behandlung/en:</span> {getServicesText()}
            </div>
          </div>

          <div className="border rounded-lg overflow-hidden flex-1 flex flex-col">
            <div className="bg-blue-50 p-3 border-b">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">Wolanin MD - Aesthetics</h3>
                  <button className="text-blue-600 text-sm hover:underline">View larger map</button>
                </div>
              </div>
            </div>
            <div className="flex-1 bg-gray-100 relative min-h-0">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2626.8234567890123!2d9.1743228!3d48.7714756!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x84ad4aabe6364d25%3A0x25b5590fa8c12bf1!2sWolanin%20MD%20-%20Aesthetics!5e0!3m2!1sen!2sde!4v1234567890123!5m2!1sen!2sde"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="absolute inset-0"
              />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                  Wolanin MD - Aesthetics
                </div>
              </div>
            </div>
            <div className="p-2 text-xs text-gray-500 border-t">
              <div className="flex justify-between items-center">
                <span>Keyboard shortcuts</span>
                <span>Map data ©2025 GeoBasis-DE/BKG (©2009), Google</span>
                <div className="flex gap-2">
                  <button className="hover:underline">Terms</button>
                  <button className="hover:underline">Report a map error</button>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-4">
            <Button variant="outline" onClick={handleReset}>
              Neuen Termin buchen
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
