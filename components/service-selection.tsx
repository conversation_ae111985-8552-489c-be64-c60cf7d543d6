"use client"

import { useEffect, useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Clock } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/lib/hooks"
import { nextStep, setServices } from "@/store/features/appointmentSlice"
import { fetchServices, setSelectedGroup } from "@/store/features/servicesSlice"
import type { Service } from "@/store/features/servicesSlice"

export function ServiceSelection() {
  const dispatch = useAppDispatch()
  const { groups, loading, selectedGroupId } = useAppSelector((state) => state.services)
  const { services: selectedServices } = useAppSelector((state) => state.appointment.appointmentData)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    dispatch(fetchServices())
  }, [dispatch])

  const toggleService = (service: Service) => {
    const isSelected = selectedServices.some((s) => s.id === service.id)
    let newSelectedServices: Service[]

    if (isSelected) {
      newSelectedServices = selectedServices.filter((s) => s.id !== service.id)
    } else {
      newSelectedServices = [...selectedServices, { ...service, selected: true }]
    }

    dispatch(setServices(newSelectedServices))
  }

  const getTotalDuration = () => {
    return selectedServices.reduce((total, service) => total + service.duration, 0)
  }

  const getTotalPrice = () => {
    return selectedServices.reduce((total, service) => total + Number.parseFloat(service.price), 0)
  }

  const handleNext = () => {
    if (selectedServices.length > 0) {
      dispatch(nextStep())
    }
  }

  // Frontend search and filtering
  const filteredServices = useMemo(() => {
    const selectedGroup = groups.find((group) => group.id === selectedGroupId)
    if (!selectedGroup) return []

    let services = selectedGroup.services

    // Apply search filter
    if (searchTerm.trim()) {
      services = services.filter(
        (service) =>
          service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          service.externalName.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    return services
  }, [groups, selectedGroupId, searchTerm])

  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto h-full">
        <Card className="w-full h-full flex items-center justify-center">
          <div>Loading services...</div>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardContent className="p-6 flex-1 flex flex-col">
          <div className="flex justify-between items-center mb-6">
            <Input
              placeholder="Suche nach Services"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
            <Button onClick={handleNext} disabled={selectedServices.length === 0}>
              Weiter
            </Button>
          </div>

          {/* Groups - Vertically scrollable tabs */}
          <div className="flex flex-wrap gap-2 mb-6 border-b pb-4 max-h-32 overflow-y-auto">
            {groups.map((group) => (
              <button
                key={group.id}
                onClick={() => dispatch(setSelectedGroup(group.id || "alle"))}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  selectedGroupId === group.id
                    ? "border-gray-900 text-gray-900"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                {group.name}
              </button>
            ))}
          </div>

          {/* Services - Horizontally scrollable grid */}
          <div className="mb-6 overflow-x-auto flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 min-w-max" style={{ minWidth: "800px" }}>
              {filteredServices.map((service) => {
                const isSelected = selectedServices.some((s) => s.id === service.id)
                return (
                  <div
                    key={service.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors bg-white ${
                      isSelected ? "border-green-500 bg-green-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => toggleService(service)}
                  >
                    <div className="flex gap-4 h-32">
                      <div className="w-32 h-32 flex-shrink-0">
                        <img
                          src="/placeholder.svg?height=128&width=128"
                          alt={service.name}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                      <div className="flex-1 flex flex-col">
                        <div className="flex-1">
                          <h3 className="font-semibold text-base mb-2">{service.name}</h3>
                          {service.externalName && service.externalName !== service.name && (
                            <p className="text-gray-600 text-sm leading-relaxed">{service.externalName}</p>
                          )}
                        </div>
                        <div className="flex items-center justify-between mt-auto pt-2">
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span>{service.duration}Min.</span>
                            <span className="ml-2">ab €{Number.parseFloat(service.price).toFixed(2)}</span>
                          </div>
                          <Checkbox checked={isSelected} onChange={() => toggleService(service)} className="ml-4" />
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {selectedServices.length > 0 && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium">Dauer: {getTotalDuration()} Min.</div>
                  <div className="font-medium">Gesamt: ab € {getTotalPrice().toFixed(2)}</div>
                </div>
                <Button onClick={handleNext}>Weiter</Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
