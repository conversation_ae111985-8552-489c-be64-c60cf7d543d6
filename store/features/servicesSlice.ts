import { createSlice, createAsyncThunk, type PayloadAction } from "@reduxjs/toolkit"

export type Service = {
  id: string
  name: string
  externalName: string
  duration: number
  price: string
  gross: number
  groupNames?: string[]
  productGroup?: string
}

export type ServiceGroup = {
  id: string | undefined
  name: string
  services: Service[]
}

interface ServicesState {
  groups: ServiceGroup[]
  allServices: Service[]
  loading: boolean
  error: string | null
  selectedGroupId: string
}

const initialState: ServicesState = {
  groups: [],
  allServices: [],
  loading: false,
  error: null,
  selectedGroupId: "alle",
}

export const fetchServices = createAsyncThunk("services/fetchServices", async () => {
  const response = await fetch("/api/services")
  if (!response.ok) {
    throw new Error("Failed to fetch services")
  }
  const data = await response.json()

  // Create "Alle" group with all services
  const allServices = data.groups.flatMap((group: ServiceGroup) => group.services)
  const alleGroup = {
    id: "alle",
    name: "Alle",
    services: allServices,
  }

  return {
    groups: [alleGroup, ...data.groups],
    allServices,
  }
})

const servicesSlice = createSlice({
  name: "services",
  initialState,
  reducers: {
    setSelectedGroup: (state, action: PayloadAction<string>) => {
      state.selectedGroupId = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchServices.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchServices.fulfilled, (state, action) => {
        state.loading = false
        state.groups = action.payload.groups
        state.allServices = action.payload.allServices
      })
      .addCase(fetchServices.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || "Failed to fetch services"
      })
  },
})

export const { setSelectedGroup } = servicesSlice.actions
export default servicesSlice.reducer
